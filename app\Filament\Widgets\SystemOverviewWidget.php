<?php

namespace App\Filament\Widgets;

use App\Models\Karyawan;
use App\Models\Absensi;
use App\Models\Schedule;
use App\Models\SopDokumen;
use App\Models\User;
use App\Traits\HasWidgetFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class SystemOverviewWidget extends BaseWidget
{
    use HasWidgetFilters;

    protected static ?string $pollingInterval = null;
    protected static bool $isLazy = false;
    protected static ?int $sort = 1;

    // hasacces
    public static function canView(): bool
    {
        return auth()->user()->hasAnyRole(['super_admin', 'direktur']);
    }

    protected function getStats(): array
    {
        // Get current filter from session
        $filters = session('dashboard_filters', ['date_range' => 'this_month']);
        $dateRange = $this->getDateRangeFromFilters($filters);

        return [
            // Total Active Employees
            Stat::make('Total Karyawan Aktif', $this->getTotalActiveEmployees())
                ->description('Karyawan dengan status aktif')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->url('/admin/karyawans'),

            // Attendance in Period
            Stat::make('Kehadiran Periode', $this->getAttendanceInPeriod($dateRange))
                ->description($this->getAttendanceDescription($dateRange))
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('success')
                ->url('/admin/absensis'),

            // Total Users
            Stat::make('Total Users', $this->getTotalUsers())
                ->description('Admin, Supervisor, Karyawan')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('info')
                ->url('/admin/users'),

            // Active SOPs
            Stat::make('SOP Aktif', $this->getActiveSops())
                ->description('Dokumen SOP yang berlaku')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('warning')
                ->url('/admin/sop-dokumens'),
        ];
    }

    private function getTotalActiveEmployees(): int
    {
        return Karyawan::where('status_aktif', true)->count();
    }

    private function getAttendanceInPeriod($dateRange): string
    {
        $totalScheduled = Schedule::whereBetween('tanggal_jadwal', [$dateRange['start'], $dateRange['end']])->count();
        $totalPresent = Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        return "{$totalPresent}/{$totalScheduled}";
    }

    private function getAttendanceDescription($dateRange): string
    {
        $totalScheduled = Schedule::whereBetween('tanggal_jadwal', [$dateRange['start'], $dateRange['end']])->count();
        $totalPresent = Absensi::with(['karyawan', 'jadwal'])
            ->whereBetween('tanggal_absensi', [$dateRange['start'], $dateRange['end']])
            ->whereIn('status', ['hadir', 'terlambat'])
            ->count();

        if ($totalScheduled === 0) {
            return 'Tidak ada jadwal pada periode ini';
        }

        $percentage = round(($totalPresent / $totalScheduled) * 100, 1);
        return "{$percentage}% hadir dari yang dijadwalkan";
    }

    private function getTotalUsers(): int
    {
        return User::count();
    }

    private function getActiveSops(): int
    {
        return SopDokumen::where('status', 'aktif')->count();
    }

    private function getDateRangeFromFilters($filters): array
    {
        $dateRange = $filters['date_range'] ?? 'this_month';

        switch ($dateRange) {
            case 'today':
                return [
                    'start' => now()->startOfDay(),
                    'end' => now()->endOfDay(),
                ];
            case 'yesterday':
                return [
                    'start' => now()->subDay()->startOfDay(),
                    'end' => now()->subDay()->endOfDay(),
                ];
            case 'this_week':
                return [
                    'start' => now()->startOfWeek(),
                    'end' => now()->endOfWeek(),
                ];
            case 'last_week':
                return [
                    'start' => now()->subWeek()->startOfWeek(),
                    'end' => now()->subWeek()->endOfWeek(),
                ];
            case 'this_month':
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
            case 'last_month':
                return [
                    'start' => now()->subMonth()->startOfMonth(),
                    'end' => now()->subMonth()->endOfMonth(),
                ];
            case 'this_quarter':
                return [
                    'start' => now()->startOfQuarter(),
                    'end' => now()->endOfQuarter(),
                ];
            case 'last_quarter':
                return [
                    'start' => now()->subQuarter()->startOfQuarter(),
                    'end' => now()->subQuarter()->endOfQuarter(),
                ];
            case 'this_year':
                return [
                    'start' => now()->startOfYear(),
                    'end' => now()->endOfYear(),
                ];
            case 'last_year':
                return [
                    'start' => now()->subYear()->startOfYear(),
                    'end' => now()->subYear()->endOfYear(),
                ];
            case 'custom':
                return [
                    'start' => $filters['start_date'] ? \Carbon\Carbon::parse($filters['start_date']) : now()->startOfMonth(),
                    'end' => $filters['end_date'] ? \Carbon\Carbon::parse($filters['end_date']) : now()->endOfMonth(),
                ];
            case 'monthly':
                $month = $filters['month'] ?? now()->month;
                $year = $filters['year'] ?? now()->year;
                return [
                    'start' => \Carbon\Carbon::create($year, $month, 1)->startOfMonth(),
                    'end' => \Carbon\Carbon::create($year, $month, 1)->endOfMonth(),
                ];
            default:
                return [
                    'start' => now()->startOfMonth(),
                    'end' => now()->endOfMonth(),
                ];
        }
    }
}
