<?php

namespace App\Filament\Resources\CutiIzinResource\Pages;

use App\Filament\Resources\CutiIzinResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateCutiIzin extends CreateRecord
{
    protected static string $resource = CutiIzinResource::class;

    public function getTitle(): string
    {
        return 'Tambah Permohonan Cuti/Izin';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // If the user is a supervisor or admin, they can auto-approve
        $user = Auth::user();
        if ($user->role === 'supervisor' || $user->role === 'admin') {
            $data['approved_by'] = $user->id;
            $data['approved_at'] = now();
            $data['status'] = 'approved';
        } else {
            $data['status'] = 'pending';
        }
        
        return $data;
    }
}
