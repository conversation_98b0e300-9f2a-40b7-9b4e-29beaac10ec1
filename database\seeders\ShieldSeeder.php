<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>zhanSalleh\FilamentShield\Support\Utils;
use <PERSON>tie\Permission\PermissionRegistrar;

class ShieldSeeder extends Seeder
{
    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $rolesWithPermissions = '[{"name":"super_admin","guard_name":"web","permissions":["view_absensi","view_any_absensi","create_absensi","update_absensi","restore_absensi","restore_any_absensi","replicate_absensi","reorder_absensi","delete_absensi","delete_any_absensi","force_delete_absensi","force_delete_any_absensi","view_akun","view_any_akun","create_akun","update_akun","restore_akun","restore_any_akun","replicate_akun","reorder_akun","delete_akun","delete_any_akun","force_delete_akun","force_delete_any_akun","view_aset","view_any_aset","create_aset","update_aset","restore_aset","restore_any_aset","replicate_aset","reorder_aset","delete_aset","delete_any_aset","force_delete_aset","force_delete_any_aset","view_aturan::keterlambatan","view_any_aturan::keterlambatan","create_aturan::keterlambatan","update_aturan::keterlambatan","restore_aturan::keterlambatan","restore_any_aturan::keterlambatan","replicate_aturan::keterlambatan","reorder_aturan::keterlambatan","delete_aturan::keterlambatan","delete_any_aturan::keterlambatan","force_delete_aturan::keterlambatan","force_delete_any_aturan::keterlambatan","view_company::settings","view_any_company::settings","create_company::settings","update_company::settings","restore_company::settings","restore_any_company::settings","replicate_company::settings","reorder_company::settings","delete_company::settings","delete_any_company::settings","force_delete_company::settings","force_delete_any_company::settings","view_departemen","view_any_departemen","create_departemen","update_departemen","restore_departemen","restore_any_departemen","replicate_departemen","reorder_departemen","delete_departemen","delete_any_departemen","force_delete_departemen","force_delete_any_departemen","view_divisi","view_any_divisi","create_divisi","update_divisi","restore_divisi","restore_any_divisi","replicate_divisi","reorder_divisi","delete_divisi","delete_any_divisi","force_delete_divisi","force_delete_any_divisi","view_entitas","view_any_entitas","create_entitas","update_entitas","restore_entitas","restore_any_entitas","replicate_entitas","reorder_entitas","delete_entitas","delete_any_entitas","force_delete_entitas","force_delete_any_entitas","view_expense::category","view_any_expense::category","create_expense::category","update_expense::category","restore_expense::category","restore_any_expense::category","replicate_expense::category","reorder_expense::category","delete_expense::category","delete_any_expense::category","force_delete_expense::category","force_delete_any_expense::category","view_expense::request","view_any_expense::request","create_expense::request","update_expense::request","restore_expense::request","restore_any_expense::request","replicate_expense::request","reorder_expense::request","delete_expense::request","delete_any_expense::request","force_delete_expense::request","force_delete_any_expense::request","view_goods::receipt","view_any_goods::receipt","create_goods::receipt","update_goods::receipt","restore_goods::receipt","restore_any_goods::receipt","replicate_goods::receipt","reorder_goods::receipt","delete_goods::receipt","delete_any_goods::receipt","force_delete_goods::receipt","force_delete_any_goods::receipt","view_inventory","view_any_inventory","create_inventory","update_inventory","restore_inventory","restore_any_inventory","replicate_inventory","reorder_inventory","delete_inventory","delete_any_inventory","force_delete_inventory","force_delete_any_inventory","view_inventory::stock","view_any_inventory::stock","create_inventory::stock","update_inventory::stock","restore_inventory::stock","restore_any_inventory::stock","replicate_inventory::stock","reorder_inventory::stock","delete_inventory::stock","delete_any_inventory::stock","force_delete_inventory::stock","force_delete_any_inventory::stock","view_jabatan","view_any_jabatan","create_jabatan","update_jabatan","restore_jabatan","restore_any_jabatan","replicate_jabatan","reorder_jabatan","delete_jabatan","delete_any_jabatan","force_delete_jabatan","force_delete_any_jabatan","view_jadwal::masal","view_any_jadwal::masal","create_jadwal::masal","update_jadwal::masal","restore_jadwal::masal","restore_any_jadwal::masal","replicate_jadwal::masal","reorder_jadwal::masal","delete_jadwal::masal","delete_any_jadwal::masal","force_delete_jadwal::masal","force_delete_any_jadwal::masal","view_jenis::pelanggaran","view_any_jenis::pelanggaran","create_jenis::pelanggaran","update_jenis::pelanggaran","restore_jenis::pelanggaran","restore_any_jenis::pelanggaran","replicate_jenis::pelanggaran","reorder_jenis::pelanggaran","delete_jenis::pelanggaran","delete_any_jenis::pelanggaran","force_delete_jenis::pelanggaran","force_delete_any_jenis::pelanggaran","view_journal","view_any_journal","create_journal","update_journal","restore_journal","restore_any_journal","replicate_journal","reorder_journal","delete_journal","delete_any_journal","force_delete_journal","force_delete_any_journal","view_karyawan","view_any_karyawan","create_karyawan","update_karyawan","restore_karyawan","restore_any_karyawan","replicate_karyawan","reorder_karyawan","delete_karyawan","delete_any_karyawan","force_delete_karyawan","force_delete_any_karyawan","view_payroll::component","view_any_payroll::component","create_payroll::component","update_payroll::component","restore_payroll::component","restore_any_payroll::component","replicate_payroll::component","reorder_payroll::component","delete_payroll::component","delete_any_payroll::component","force_delete_payroll::component","force_delete_any_payroll::component","view_payroll::period","view_any_payroll::period","create_payroll::period","update_payroll::period","restore_payroll::period","restore_any_payroll::period","replicate_payroll::period","reorder_payroll::period","delete_payroll::period","delete_any_payroll::period","force_delete_payroll::period","force_delete_any_payroll::period","view_payroll::transaction","view_any_payroll::transaction","create_payroll::transaction","update_payroll::transaction","restore_payroll::transaction","restore_any_payroll::transaction","replicate_payroll::transaction","reorder_payroll::transaction","delete_payroll::transaction","delete_any_payroll::transaction","force_delete_payroll::transaction","force_delete_any_payroll::transaction","view_petty::cash::fund","view_any_petty::cash::fund","create_petty::cash::fund","update_petty::cash::fund","restore_petty::cash::fund","restore_any_petty::cash::fund","replicate_petty::cash::fund","reorder_petty::cash::fund","delete_petty::cash::fund","delete_any_petty::cash::fund","force_delete_petty::cash::fund","force_delete_any_petty::cash::fund","view_posting::rule","view_any_posting::rule","create_posting::rule","update_posting::rule","restore_posting::rule","restore_any_posting::rule","replicate_posting::rule","reorder_posting::rule","delete_posting::rule","delete_any_posting::rule","force_delete_posting::rule","force_delete_any_posting::rule","view_produk","view_any_produk","create_produk","update_produk","restore_produk","restore_any_produk","replicate_produk","reorder_produk","delete_produk","delete_any_produk","force_delete_produk","force_delete_any_produk","view_project","view_any_project","create_project","update_project","restore_project","restore_any_project","replicate_project","reorder_project","delete_project","delete_any_project","force_delete_project","force_delete_any_project","view_ptkp::rate","view_any_ptkp::rate","create_ptkp::rate","update_ptkp::rate","restore_ptkp::rate","restore_any_ptkp::rate","replicate_ptkp::rate","reorder_ptkp::rate","delete_ptkp::rate","delete_any_ptkp::rate","force_delete_ptkp::rate","force_delete_any_ptkp::rate","view_purchase::order","view_any_purchase::order","create_purchase::order","update_purchase::order","restore_purchase::order","restore_any_purchase::order","replicate_purchase::order","reorder_purchase::order","delete_purchase::order","delete_any_purchase::order","force_delete_purchase::order","force_delete_any_purchase::order","view_role","view_any_role","create_role","update_role","delete_role","delete_any_role","view_sales::transaction","view_any_sales::transaction","create_sales::transaction","update_sales::transaction","restore_sales::transaction","restore_any_sales::transaction","replicate_sales::transaction","reorder_sales::transaction","delete_sales::transaction","delete_any_sales::transaction","force_delete_sales::transaction","force_delete_any_sales::transaction","view_satuan","view_any_satuan","create_satuan","update_satuan","restore_satuan","restore_any_satuan","replicate_satuan","reorder_satuan","delete_satuan","delete_any_satuan","force_delete_satuan","force_delete_any_satuan","view_shift","view_any_shift","create_shift","update_shift","restore_shift","restore_any_shift","replicate_shift","reorder_shift","delete_shift","delete_any_shift","force_delete_shift","force_delete_any_shift","view_sop::dokumen","view_any_sop::dokumen","create_sop::dokumen","update_sop::dokumen","restore_sop::dokumen","restore_any_sop::dokumen","replicate_sop::dokumen","reorder_sop::dokumen","delete_sop::dokumen","delete_any_sop::dokumen","force_delete_sop::dokumen","force_delete_any_sop::dokumen","view_stock::adjustment","view_any_stock::adjustment","create_stock::adjustment","update_stock::adjustment","restore_stock::adjustment","restore_any_stock::adjustment","replicate_stock::adjustment","reorder_stock::adjustment","delete_stock::adjustment","delete_any_stock::adjustment","force_delete_stock::adjustment","force_delete_any_stock::adjustment","view_stock::movement","view_any_stock::movement","create_stock::movement","update_stock::movement","restore_stock::movement","restore_any_stock::movement","replicate_stock::movement","reorder_stock::movement","delete_stock::movement","delete_any_stock::movement","force_delete_stock::movement","force_delete_any_stock::movement","view_stock::opname","view_any_stock::opname","create_stock::opname","update_stock::opname","restore_stock::opname","restore_any_stock::opname","replicate_stock::opname","reorder_stock::opname","delete_stock::opname","delete_any_stock::opname","force_delete_stock::opname","force_delete_any_stock::opname","view_stock::transfer","view_any_stock::transfer","create_stock::transfer","update_stock::transfer","restore_stock::transfer","restore_any_stock::transfer","replicate_stock::transfer","reorder_stock::transfer","delete_stock::transfer","delete_any_stock::transfer","force_delete_stock::transfer","force_delete_any_stock::transfer","view_struktur::organisasi","view_any_struktur::organisasi","create_struktur::organisasi","update_struktur::organisasi","restore_struktur::organisasi","restore_any_struktur::organisasi","replicate_struktur::organisasi","reorder_struktur::organisasi","delete_struktur::organisasi","delete_any_struktur::organisasi","force_delete_struktur::organisasi","force_delete_any_struktur::organisasi","view_task","view_any_task","create_task","update_task","restore_task","restore_any_task","replicate_task","reorder_task","delete_task","delete_any_task","force_delete_task","force_delete_any_task","view_tax::bracket","view_any_tax::bracket","create_tax::bracket","update_tax::bracket","restore_tax::bracket","restore_any_tax::bracket","replicate_tax::bracket","reorder_tax::bracket","delete_tax::bracket","delete_any_tax::bracket","force_delete_tax::bracket","force_delete_any_tax::bracket","view_unit","view_any_unit","create_unit","update_unit","restore_unit","restore_any_unit","replicate_unit","reorder_unit","delete_unit","delete_any_unit","force_delete_unit","force_delete_any_unit","view_user","view_any_user","create_user","update_user","restore_user","restore_any_user","replicate_user","reorder_user","delete_user","delete_any_user","force_delete_user","force_delete_any_user","view_warehouse","view_any_warehouse","create_warehouse","update_warehouse","restore_warehouse","restore_any_warehouse","replicate_warehouse","reorder_warehouse","delete_warehouse","delete_any_warehouse","force_delete_warehouse","force_delete_any_warehouse","page_GeneralLedger","page_AbsensiDashboard","page_BalanceSheet","page_EnhancedKanbanBoard","page_EnhancedProjectDashboard","page_HRDashboard","page_ImportSales","page_IncomeStatement","page_InventoryDashboard","page_KanbanBoard","page_PayrollDashboard","page_PerformanceDashboard","page_ProjectOverview","page_SupervisorDashboard","page_Timesheets","page_UserKaryawanManager","page_WorkPeriodSettings","widget_AbsensiTrendChart","widget_AttendanceOverview","widget_HrDepartmentChart","widget_InventoryStatsWidget","widget_PayrollStatsOverview","widget_PayrollTrendChart","widget_PerformanceGradeChart","widget_ProjectCalendarView","widget_ProjectGanttChart","widget_ProjectHealthOverview","widget_ProjectOverviewStats","widget_ProjectViewToggle","widget_SalaryChart","widget_SopOverview","widget_SupervisorStatsOverview","widget_AbsensiOverviewWidget","widget_EnhancedProjectStats","widget_HROverviewWidget","widget_HrStatsWidget","widget_PayrollOverviewWidget","widget_PerformanceOverviewWidget","widget_SystemOverviewWidget","widget_AttendanceTrendsWidget","widget_EmployeeDemographicsWidget","widget_PayrollTrendsWidget","widget_ProjectManagementWidget","widget_QuickStatsWidget","widget_ScheduleTable","widget_AttendanceByDepartmentWidget","widget_PayrollByDepartmentWidget","widget_PerformanceAnalyticsWidget","widget_ProjectTimelineWidget","widget_RecentActivitiesWidget","widget_TeamPerformanceChart","widget_CompensationBreakdownWidget","widget_HRAlertsWidget","widget_LateArrivalsWidget","widget_AbsensiStatusWidget","widget_PayrollAlertsWidget","widget_RecentProjectActivity","widget_SalaryAnalyticsWidget","widget_AttendanceAlertsWidget","widget_AttendanceAnalyticsWidget","widget_ProjectResourceAllocation","view_karyawan::profile","view_any_karyawan::profile","create_karyawan::profile","update_karyawan::profile","restore_karyawan::profile","restore_any_karyawan::profile","replicate_karyawan::profile","reorder_karyawan::profile","delete_karyawan::profile","delete_any_karyawan::profile","force_delete_karyawan::profile","force_delete_any_karyawan::profile","view_schedule","view_any_schedule","create_schedule","update_schedule","restore_schedule","restore_any_schedule","replicate_schedule","reorder_schedule","delete_schedule","delete_any_schedule","force_delete_schedule","force_delete_any_schedule","view_sop","view_any_sop","create_sop","update_sop","restore_sop","restore_any_sop","replicate_sop","reorder_sop","delete_sop","delete_any_sop","force_delete_sop","force_delete_any_sop"]},{"name":"manager_hrd","guard_name":"web","permissions":["view_absensi","view_any_absensi","create_absensi","update_absensi","restore_absensi","restore_any_absensi","replicate_absensi","reorder_absensi","delete_absensi","delete_any_absensi","force_delete_absensi","force_delete_any_absensi","view_departemen","view_any_departemen","create_departemen","update_departemen","restore_departemen","restore_any_departemen","replicate_departemen","reorder_departemen","delete_departemen","delete_any_departemen","force_delete_departemen","force_delete_any_departemen","view_divisi","view_any_divisi","create_divisi","update_divisi","restore_divisi","restore_any_divisi","replicate_divisi","reorder_divisi","delete_divisi","delete_any_divisi","force_delete_divisi","force_delete_any_divisi","view_jabatan","view_any_jabatan","create_jabatan","update_jabatan","restore_jabatan","restore_any_jabatan","replicate_jabatan","reorder_jabatan","delete_jabatan","delete_any_jabatan","force_delete_jabatan","force_delete_any_jabatan","view_karyawan","view_any_karyawan","create_karyawan","update_karyawan","restore_karyawan","restore_any_karyawan","replicate_karyawan","reorder_karyawan","delete_karyawan","delete_any_karyawan","force_delete_karyawan","force_delete_any_karyawan","view_project","view_any_project","create_project","update_project","restore_project","restore_any_project","replicate_project","reorder_project","delete_project","delete_any_project","force_delete_project","force_delete_any_project","view_shift","view_any_shift","create_shift","update_shift","restore_shift","restore_any_shift","replicate_shift","reorder_shift","delete_shift","delete_any_shift","force_delete_shift","force_delete_any_shift","view_task","view_any_task","create_task","update_task","restore_task","restore_any_task","replicate_task","reorder_task","delete_task","delete_any_task","force_delete_task","force_delete_any_task","view_user","view_any_user","create_user","update_user","restore_user","restore_any_user","replicate_user","reorder_user","delete_user","delete_any_user","force_delete_user","force_delete_any_user","page_AbsensiDashboard","page_EnhancedKanbanBoard","page_EnhancedProjectDashboard","page_HRDashboard","page_KanbanBoard","page_PayrollDashboard","page_PerformanceDashboard","page_ProjectOverview","page_SupervisorDashboard","page_Timesheets","page_UserKaryawanManager","page_WorkPeriodSettings","widget_AbsensiTrendChart","widget_AttendanceOverview","widget_HrDepartmentChart","widget_PayrollStatsOverview","widget_PayrollTrendChart","widget_PerformanceGradeChart","widget_ProjectCalendarView","widget_ProjectGanttChart","widget_ProjectHealthOverview","widget_ProjectOverviewStats","widget_ProjectViewToggle","widget_SalaryChart","widget_AbsensiOverviewWidget","widget_EnhancedProjectStats","widget_HROverviewWidget","widget_HrStatsWidget","widget_PayrollOverviewWidget","widget_PerformanceOverviewWidget","widget_AttendanceTrendsWidget","widget_EmployeeDemographicsWidget","widget_PayrollTrendsWidget","widget_ProjectManagementWidget","widget_QuickStatsWidget","widget_ScheduleTable","widget_AttendanceByDepartmentWidget","widget_PayrollByDepartmentWidget","widget_PerformanceAnalyticsWidget","widget_ProjectTimelineWidget","widget_RecentActivitiesWidget","widget_TeamPerformanceChart","widget_CompensationBreakdownWidget","widget_HRAlertsWidget","widget_LateArrivalsWidget","widget_AbsensiStatusWidget","widget_PayrollAlertsWidget","widget_RecentProjectActivity","widget_SalaryAnalyticsWidget","widget_AttendanceAlertsWidget","widget_AttendanceAnalyticsWidget","widget_ProjectResourceAllocation","view_karyawan::profile","view_any_karyawan::profile","create_karyawan::profile","update_karyawan::profile","restore_karyawan::profile","restore_any_karyawan::profile","replicate_karyawan::profile","reorder_karyawan::profile","delete_karyawan::profile","delete_any_karyawan::profile","force_delete_karyawan::profile","force_delete_any_karyawan::profile"]},{"name":"manager_accounting","guard_name":"web","permissions":["view_akun","view_any_akun","create_akun","update_akun","restore_akun","restore_any_akun","replicate_akun","reorder_akun","delete_akun","delete_any_akun","force_delete_akun","force_delete_any_akun","view_aset","view_any_aset","create_aset","update_aset","restore_aset","restore_any_aset","replicate_aset","reorder_aset","delete_aset","delete_any_aset","force_delete_aset","force_delete_any_aset","view_departemen","view_any_departemen","create_departemen","update_departemen","restore_departemen","restore_any_departemen","replicate_departemen","reorder_departemen","delete_departemen","delete_any_departemen","force_delete_departemen","force_delete_any_departemen","view_divisi","view_any_divisi","create_divisi","update_divisi","restore_divisi","restore_any_divisi","replicate_divisi","reorder_divisi","delete_divisi","delete_any_divisi","force_delete_divisi","force_delete_any_divisi","view_entitas","view_any_entitas","create_entitas","update_entitas","restore_entitas","restore_any_entitas","replicate_entitas","reorder_entitas","delete_entitas","delete_any_entitas","force_delete_entitas","force_delete_any_entitas","view_inventory","view_any_inventory","create_inventory","update_inventory","restore_inventory","restore_any_inventory","replicate_inventory","reorder_inventory","delete_inventory","delete_any_inventory","force_delete_inventory","force_delete_any_inventory","view_inventory::stock","view_any_inventory::stock","create_inventory::stock","update_inventory::stock","restore_inventory::stock","restore_any_inventory::stock","replicate_inventory::stock","reorder_inventory::stock","delete_inventory::stock","delete_any_inventory::stock","force_delete_inventory::stock","force_delete_any_inventory::stock","view_journal","view_any_journal","create_journal","update_journal","restore_journal","restore_any_journal","replicate_journal","reorder_journal","delete_journal","delete_any_journal","force_delete_journal","force_delete_any_journal","view_karyawan","view_any_karyawan","create_karyawan","update_karyawan","restore_karyawan","restore_any_karyawan","replicate_karyawan","reorder_karyawan","delete_karyawan","delete_any_karyawan","force_delete_karyawan","force_delete_any_karyawan","view_produk","view_any_produk","create_produk","update_produk","restore_produk","restore_any_produk","replicate_produk","reorder_produk","delete_produk","delete_any_produk","force_delete_produk","force_delete_any_produk","view_project","view_any_project","create_project","update_project","restore_project","restore_any_project","replicate_project","reorder_project","delete_project","delete_any_project","force_delete_project","force_delete_any_project","view_satuan","view_any_satuan","create_satuan","update_satuan","restore_satuan","restore_any_satuan","replicate_satuan","reorder_satuan","delete_satuan","delete_any_satuan","force_delete_satuan","force_delete_any_satuan","view_task","view_any_task","create_task","update_task","restore_task","restore_any_task","replicate_task","reorder_task","delete_task","delete_any_task","force_delete_task","force_delete_any_task","view_unit","view_any_unit","create_unit","update_unit","restore_unit","restore_any_unit","replicate_unit","reorder_unit","delete_unit","delete_any_unit","force_delete_unit","force_delete_any_unit","view_user","view_any_user","create_user","update_user","restore_user","restore_any_user","replicate_user","reorder_user","delete_user","delete_any_user","force_delete_user","force_delete_any_user","view_warehouse","view_any_warehouse","create_warehouse","update_warehouse","restore_warehouse","restore_any_warehouse","replicate_warehouse","reorder_warehouse","delete_warehouse","delete_any_warehouse","force_delete_warehouse","force_delete_any_warehouse","page_GeneralLedger","page_BalanceSheet","page_EnhancedKanbanBoard","page_EnhancedProjectDashboard","page_ImportSales","page_IncomeStatement","page_InventoryDashboard","page_KanbanBoard","page_PayrollDashboard","page_ProjectOverview","page_Timesheets","widget_InventoryStatsWidget","widget_PayrollStatsOverview","widget_PayrollTrendChart","widget_PayrollOverviewWidget","widget_SystemOverviewWidget","widget_PayrollTrendsWidget","widget_QuickStatsWidget","widget_PayrollByDepartmentWidget","widget_CompensationBreakdownWidget","widget_PayrollAlertsWidget","widget_SalaryAnalyticsWidget","view_karyawan::profile","view_any_karyawan::profile","create_karyawan::profile","update_karyawan::profile","restore_karyawan::profile","restore_any_karyawan::profile","replicate_karyawan::profile","reorder_karyawan::profile","delete_karyawan::profile","delete_any_karyawan::profile","force_delete_karyawan::profile","force_delete_any_karyawan::profile"]},{"name":"direktur","guard_name":"web","permissions":["view_absensi","view_any_absensi","create_absensi","update_absensi","restore_absensi","restore_any_absensi","replicate_absensi","reorder_absensi","delete_absensi","delete_any_absensi","force_delete_absensi","force_delete_any_absensi","view_akun","view_any_akun","create_akun","update_akun","restore_akun","restore_any_akun","replicate_akun","reorder_akun","delete_akun","delete_any_akun","force_delete_akun","force_delete_any_akun","view_aset","view_any_aset","create_aset","update_aset","restore_aset","restore_any_aset","replicate_aset","reorder_aset","delete_aset","delete_any_aset","force_delete_aset","force_delete_any_aset","view_aturan::keterlambatan","view_any_aturan::keterlambatan","create_aturan::keterlambatan","update_aturan::keterlambatan","restore_aturan::keterlambatan","restore_any_aturan::keterlambatan","replicate_aturan::keterlambatan","reorder_aturan::keterlambatan","delete_aturan::keterlambatan","delete_any_aturan::keterlambatan","force_delete_aturan::keterlambatan","force_delete_any_aturan::keterlambatan","view_company::settings","view_any_company::settings","create_company::settings","update_company::settings","restore_company::settings","restore_any_company::settings","replicate_company::settings","reorder_company::settings","delete_company::settings","delete_any_company::settings","force_delete_company::settings","force_delete_any_company::settings","view_departemen","view_any_departemen","create_departemen","update_departemen","restore_departemen","restore_any_departemen","replicate_departemen","reorder_departemen","delete_departemen","delete_any_departemen","force_delete_departemen","force_delete_any_departemen","view_divisi","view_any_divisi","create_divisi","update_divisi","restore_divisi","restore_any_divisi","replicate_divisi","reorder_divisi","delete_divisi","delete_any_divisi","force_delete_divisi","force_delete_any_divisi","view_entitas","view_any_entitas","create_entitas","update_entitas","restore_entitas","restore_any_entitas","replicate_entitas","reorder_entitas","delete_entitas","delete_any_entitas","force_delete_entitas","force_delete_any_entitas","view_expense::category","view_any_expense::category","create_expense::category","update_expense::category","restore_expense::category","restore_any_expense::category","replicate_expense::category","reorder_expense::category","delete_expense::category","delete_any_expense::category","force_delete_expense::category","force_delete_any_expense::category","view_expense::request","view_any_expense::request","create_expense::request","update_expense::request","restore_expense::request","restore_any_expense::request","replicate_expense::request","reorder_expense::request","delete_expense::request","delete_any_expense::request","force_delete_expense::request","force_delete_any_expense::request","view_goods::receipt","view_any_goods::receipt","create_goods::receipt","update_goods::receipt","restore_goods::receipt","restore_any_goods::receipt","replicate_goods::receipt","reorder_goods::receipt","delete_goods::receipt","delete_any_goods::receipt","force_delete_goods::receipt","force_delete_any_goods::receipt","view_inventory","view_any_inventory","create_inventory","update_inventory","restore_inventory","restore_any_inventory","replicate_inventory","reorder_inventory","delete_inventory","delete_any_inventory","force_delete_inventory","force_delete_any_inventory","view_inventory::stock","view_any_inventory::stock","create_inventory::stock","update_inventory::stock","restore_inventory::stock","restore_any_inventory::stock","replicate_inventory::stock","reorder_inventory::stock","delete_inventory::stock","delete_any_inventory::stock","force_delete_inventory::stock","force_delete_any_inventory::stock","view_jabatan","view_any_jabatan","create_jabatan","update_jabatan","restore_jabatan","restore_any_jabatan","replicate_jabatan","reorder_jabatan","delete_jabatan","delete_any_jabatan","force_delete_jabatan","force_delete_any_jabatan","view_jadwal::masal","view_any_jadwal::masal","create_jadwal::masal","update_jadwal::masal","restore_jadwal::masal","restore_any_jadwal::masal","replicate_jadwal::masal","reorder_jadwal::masal","delete_jadwal::masal","delete_any_jadwal::masal","force_delete_jadwal::masal","force_delete_any_jadwal::masal","view_jenis::pelanggaran","view_any_jenis::pelanggaran","create_jenis::pelanggaran","update_jenis::pelanggaran","restore_jenis::pelanggaran","restore_any_jenis::pelanggaran","replicate_jenis::pelanggaran","reorder_jenis::pelanggaran","delete_jenis::pelanggaran","delete_any_jenis::pelanggaran","force_delete_jenis::pelanggaran","force_delete_any_jenis::pelanggaran","view_journal","view_any_journal","create_journal","update_journal","restore_journal","restore_any_journal","replicate_journal","reorder_journal","delete_journal","delete_any_journal","force_delete_journal","force_delete_any_journal","view_karyawan","view_any_karyawan","create_karyawan","update_karyawan","restore_karyawan","restore_any_karyawan","replicate_karyawan","reorder_karyawan","delete_karyawan","delete_any_karyawan","force_delete_karyawan","force_delete_any_karyawan","view_payroll::component","view_any_payroll::component","create_payroll::component","update_payroll::component","restore_payroll::component","restore_any_payroll::component","replicate_payroll::component","reorder_payroll::component","delete_payroll::component","delete_any_payroll::component","force_delete_payroll::component","force_delete_any_payroll::component","view_payroll::period","view_any_payroll::period","create_payroll::period","update_payroll::period","restore_payroll::period","restore_any_payroll::period","replicate_payroll::period","reorder_payroll::period","delete_payroll::period","delete_any_payroll::period","force_delete_payroll::period","force_delete_any_payroll::period","view_payroll::transaction","view_any_payroll::transaction","create_payroll::transaction","update_payroll::transaction","restore_payroll::transaction","restore_any_payroll::transaction","replicate_payroll::transaction","reorder_payroll::transaction","delete_payroll::transaction","delete_any_payroll::transaction","force_delete_payroll::transaction","force_delete_any_payroll::transaction","view_petty::cash::fund","view_any_petty::cash::fund","create_petty::cash::fund","update_petty::cash::fund","restore_petty::cash::fund","restore_any_petty::cash::fund","replicate_petty::cash::fund","reorder_petty::cash::fund","delete_petty::cash::fund","delete_any_petty::cash::fund","force_delete_petty::cash::fund","force_delete_any_petty::cash::fund","view_posting::rule","view_any_posting::rule","create_posting::rule","update_posting::rule","restore_posting::rule","restore_any_posting::rule","replicate_posting::rule","reorder_posting::rule","delete_posting::rule","delete_any_posting::rule","force_delete_posting::rule","force_delete_any_posting::rule","view_produk","view_any_produk","create_produk","update_produk","restore_produk","restore_any_produk","replicate_produk","reorder_produk","delete_produk","delete_any_produk","force_delete_produk","force_delete_any_produk","view_project","view_any_project","create_project","update_project","restore_project","restore_any_project","replicate_project","reorder_project","delete_project","delete_any_project","force_delete_project","force_delete_any_project","view_ptkp::rate","view_any_ptkp::rate","create_ptkp::rate","update_ptkp::rate","restore_ptkp::rate","restore_any_ptkp::rate","replicate_ptkp::rate","reorder_ptkp::rate","delete_ptkp::rate","delete_any_ptkp::rate","force_delete_ptkp::rate","force_delete_any_ptkp::rate","view_purchase::order","view_any_purchase::order","create_purchase::order","update_purchase::order","restore_purchase::order","restore_any_purchase::order","replicate_purchase::order","reorder_purchase::order","delete_purchase::order","delete_any_purchase::order","force_delete_purchase::order","force_delete_any_purchase::order","view_role","view_any_role","create_role","update_role","delete_role","delete_any_role","view_sales::transaction","view_any_sales::transaction","create_sales::transaction","update_sales::transaction","restore_sales::transaction","restore_any_sales::transaction","replicate_sales::transaction","reorder_sales::transaction","delete_sales::transaction","delete_any_sales::transaction","force_delete_sales::transaction","force_delete_any_sales::transaction","view_satuan","view_any_satuan","create_satuan","update_satuan","restore_satuan","restore_any_satuan","replicate_satuan","reorder_satuan","delete_satuan","delete_any_satuan","force_delete_satuan","force_delete_any_satuan","view_shift","view_any_shift","create_shift","update_shift","restore_shift","restore_any_shift","replicate_shift","reorder_shift","delete_shift","delete_any_shift","force_delete_shift","force_delete_any_shift","view_sop::dokumen","view_any_sop::dokumen","create_sop::dokumen","update_sop::dokumen","restore_sop::dokumen","restore_any_sop::dokumen","replicate_sop::dokumen","reorder_sop::dokumen","delete_sop::dokumen","delete_any_sop::dokumen","force_delete_sop::dokumen","force_delete_any_sop::dokumen","view_stock::adjustment","view_any_stock::adjustment","create_stock::adjustment","update_stock::adjustment","restore_stock::adjustment","restore_any_stock::adjustment","replicate_stock::adjustment","reorder_stock::adjustment","delete_stock::adjustment","delete_any_stock::adjustment","force_delete_stock::adjustment","force_delete_any_stock::adjustment","view_stock::movement","view_any_stock::movement","create_stock::movement","update_stock::movement","restore_stock::movement","restore_any_stock::movement","replicate_stock::movement","reorder_stock::movement","delete_stock::movement","delete_any_stock::movement","force_delete_stock::movement","force_delete_any_stock::movement","view_stock::opname","view_any_stock::opname","create_stock::opname","update_stock::opname","restore_stock::opname","restore_any_stock::opname","replicate_stock::opname","reorder_stock::opname","delete_stock::opname","delete_any_stock::opname","force_delete_stock::opname","force_delete_any_stock::opname","view_stock::transfer","view_any_stock::transfer","create_stock::transfer","update_stock::transfer","restore_stock::transfer","restore_any_stock::transfer","replicate_stock::transfer","reorder_stock::transfer","delete_stock::transfer","delete_any_stock::transfer","force_delete_stock::transfer","force_delete_any_stock::transfer","view_struktur::organisasi","view_any_struktur::organisasi","create_struktur::organisasi","update_struktur::organisasi","restore_struktur::organisasi","restore_any_struktur::organisasi","replicate_struktur::organisasi","reorder_struktur::organisasi","delete_struktur::organisasi","delete_any_struktur::organisasi","force_delete_struktur::organisasi","force_delete_any_struktur::organisasi","view_task","view_any_task","create_task","update_task","restore_task","restore_any_task","replicate_task","reorder_task","delete_task","delete_any_task","force_delete_task","force_delete_any_task","view_tax::bracket","view_any_tax::bracket","create_tax::bracket","update_tax::bracket","restore_tax::bracket","restore_any_tax::bracket","replicate_tax::bracket","reorder_tax::bracket","delete_tax::bracket","delete_any_tax::bracket","force_delete_tax::bracket","force_delete_any_tax::bracket","view_unit","view_any_unit","create_unit","update_unit","restore_unit","restore_any_unit","replicate_unit","reorder_unit","delete_unit","delete_any_unit","force_delete_unit","force_delete_any_unit","view_user","view_any_user","create_user","update_user","restore_user","restore_any_user","replicate_user","reorder_user","delete_user","delete_any_user","force_delete_user","force_delete_any_user","view_warehouse","view_any_warehouse","create_warehouse","update_warehouse","restore_warehouse","restore_any_warehouse","replicate_warehouse","reorder_warehouse","delete_warehouse","delete_any_warehouse","force_delete_warehouse","force_delete_any_warehouse","page_GeneralLedger","page_AbsensiDashboard","page_BalanceSheet","page_EnhancedKanbanBoard","page_EnhancedProjectDashboard","page_HRDashboard","page_ImportSales","page_IncomeStatement","page_InventoryDashboard","page_KanbanBoard","page_PayrollDashboard","page_PerformanceDashboard","page_ProjectOverview","page_SupervisorDashboard","page_Timesheets","page_UserKaryawanManager","page_WorkPeriodSettings","widget_AbsensiTrendChart","widget_AttendanceOverview","widget_HrDepartmentChart","widget_InventoryStatsWidget","widget_PayrollStatsOverview","widget_PayrollTrendChart","widget_PerformanceGradeChart","widget_ProjectCalendarView","widget_ProjectGanttChart","widget_ProjectHealthOverview","widget_ProjectOverviewStats","widget_ProjectViewToggle","widget_SalaryChart","widget_SopOverview","widget_SupervisorStatsOverview","widget_AbsensiOverviewWidget","widget_EnhancedProjectStats","widget_HROverviewWidget","widget_HrStatsWidget","widget_PayrollOverviewWidget","widget_PerformanceOverviewWidget","widget_SystemOverviewWidget","widget_AttendanceTrendsWidget","widget_EmployeeDemographicsWidget","widget_PayrollTrendsWidget","widget_ProjectManagementWidget","widget_QuickStatsWidget","widget_ScheduleTable","widget_AttendanceByDepartmentWidget","widget_PayrollByDepartmentWidget","widget_PerformanceAnalyticsWidget","widget_ProjectTimelineWidget","widget_RecentActivitiesWidget","widget_TeamPerformanceChart","widget_CompensationBreakdownWidget","widget_HRAlertsWidget","widget_LateArrivalsWidget","widget_AbsensiStatusWidget","widget_PayrollAlertsWidget","widget_RecentProjectActivity","widget_SalaryAnalyticsWidget","widget_AttendanceAlertsWidget","widget_AttendanceAnalyticsWidget","widget_ProjectResourceAllocation","view_karyawan::profile","view_any_karyawan::profile","create_karyawan::profile","update_karyawan::profile","restore_karyawan::profile","restore_any_karyawan::profile","replicate_karyawan::profile","reorder_karyawan::profile","delete_karyawan::profile","delete_any_karyawan::profile","force_delete_karyawan::profile","force_delete_any_karyawan::profile","view_schedule","view_any_schedule","create_schedule","update_schedule","restore_schedule","restore_any_schedule","replicate_schedule","reorder_schedule","delete_schedule","delete_any_schedule","force_delete_schedule","force_delete_any_schedule","view_sop","view_any_sop","create_sop","update_sop","restore_sop","restore_any_sop","replicate_sop","reorder_sop","delete_sop","delete_any_sop","force_delete_sop","force_delete_any_sop"]},{"name":"karyawan","guard_name":"web","permissions":["view_absensi","view_any_absensi","view_karyawan","view_any_karyawan","view_project","view_any_project","view_shift","view_any_shift","view_task","view_any_task","page_AbsensiDashboard","page_KanbanBoard","page_ProjectOverview","page_Timesheets","widget_ProjectCalendarView","widget_ProjectHealthOverview","widget_ProjectOverviewStats","widget_ProjectViewToggle","widget_AbsensiOverviewWidget","widget_SystemOverviewWidget","widget_QuickStatsWidget","view_karyawan::profile","view_any_karyawan::profile"]}]';
        $directPermissions = '[]';

        static::makeRolesWithPermissions($rolesWithPermissions);
        static::makeDirectPermissions($directPermissions);

        $this->command->info('Shield Seeding Completed.');
    }

    protected static function makeRolesWithPermissions(string $rolesWithPermissions): void
    {
        if (! blank($rolePlusPermissions = json_decode($rolesWithPermissions, true))) {
            /** @var Model $roleModel */
            $roleModel = Utils::getRoleModel();
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($rolePlusPermissions as $rolePlusPermission) {
                $role = $roleModel::firstOrCreate([
                    'name' => $rolePlusPermission['name'],
                    'guard_name' => $rolePlusPermission['guard_name'],
                ]);

                if (! blank($rolePlusPermission['permissions'])) {
                    $permissionModels = collect($rolePlusPermission['permissions'])
                        ->map(fn ($permission) => $permissionModel::firstOrCreate([
                            'name' => $permission,
                            'guard_name' => $rolePlusPermission['guard_name'],
                        ]))
                        ->all();

                    $role->syncPermissions($permissionModels);
                }
            }
        }
    }

    public static function makeDirectPermissions(string $directPermissions): void
    {
        if (! blank($permissions = json_decode($directPermissions, true))) {
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($permissions as $permission) {
                if ($permissionModel::whereName($permission)->doesntExist()) {
                    $permissionModel::create([
                        'name' => $permission['name'],
                        'guard_name' => $permission['guard_name'],
                    ]);
                }
            }
        }
    }
}
