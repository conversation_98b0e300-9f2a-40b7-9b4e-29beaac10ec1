<?php

namespace App\Filament\Resources\CutiIzinResource\Pages;

use App\Filament\Resources\CutiIzinResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCutiIzin extends EditRecord
{
    protected static string $resource = CutiIzinResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return 'Edit Permohonan Cuti/Izin';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
