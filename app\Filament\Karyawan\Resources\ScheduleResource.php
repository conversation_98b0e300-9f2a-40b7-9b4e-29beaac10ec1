<?php

namespace App\Filament\Karyawan\Resources;

use App\Filament\Karyawan\Resources\ScheduleResource\Pages;
use App\Models\Schedule;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ScheduleResource extends Resource
{
    protected static ?string $model = Schedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationLabel = 'Jadwal Saya';

    protected static ?string $modelLabel = 'Jadwal';

    protected static ?string $pluralModelLabel = 'Jadwal Saya';

    protected static ?int $navigationSort = 3;

    public static function canAccess(): bool
    {
        return Auth::user()->role === 'karyawan';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena read-only untuk karyawan
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tanggal_jadwal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('shift.waktu_masuk')
                    ->label('Waktu Masuk')
                    ->time('H:i'),

                Tables\Columns\TextColumn::make('shift.waktu_keluar')
                    ->label('Waktu Keluar')
                    ->time('H:i'),

                Tables\Columns\TextColumn::make('karyawan.entitas.nama_entitas')
                    ->label('Lokasi')
                    ->badge()
                    ->color('success')
                    ->placeholder('Tidak ada lokasi'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'scheduled' => 'primary',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->placeholder('Tidak ada status'),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(30)
                    ->placeholder('Tidak ada keterangan'),

                Tables\Columns\IconColumn::make('is_approved')
                    ->label('Disetujui')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-clock')
                    ->trueColor('success')
                    ->falseColor('warning'),
            ])
            ->filters([
                Tables\Filters\Filter::make('minggu_ini')
                    ->label('Minggu Ini')
                    ->query(fn(Builder $query): Builder => $query->whereBetween('tanggal_jadwal', [
                        now()->startOfWeek(),
                        now()->endOfWeek()
                    ])),

                Tables\Filters\Filter::make('bulan_ini')
                    ->label('Bulan Ini')
                    ->query(fn(Builder $query): Builder => $query->whereBetween('tanggal_jadwal', [
                        now()->startOfMonth(),
                        now()->endOfMonth()
                    ])),

                Tables\Filters\SelectFilter::make('shift_id')
                    ->label('Shift')
                    ->relationship('shift', 'nama_shift'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Jadwal'),
            ])
            ->bulkActions([
                // No bulk actions for employee view
            ])
            ->defaultSort('tanggal_jadwal', 'desc')
            ->emptyStateHeading('Tidak Ada Jadwal')
            ->emptyStateDescription('Belum ada jadwal yang ditetapkan.')
            ->emptyStateIcon('heroicon-o-calendar-days');
    }

    public static function getEloquentQuery(): Builder
    {
        $user = Auth::user();
        $karyawan = $user->karyawan;

        if (!$karyawan) {
            return parent::getEloquentQuery()->whereRaw('1 = 0'); // Return empty query
        }

        return parent::getEloquentQuery()
            ->where('karyawan_id', $karyawan->id)
            ->with(['shift', 'karyawan.entitas']);
    }

    public static function canCreate(): bool
    {
        return false; // Karyawan tidak bisa membuat jadwal
    }

    public static function canEdit($record): bool
    {
        return false; // Karyawan tidak bisa edit jadwal
    }

    public static function canDelete($record): bool
    {
        return false; // Karyawan tidak bisa hapus jadwal
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSchedules::route('/'),
            'view' => Pages\ViewSchedule::route('/{record}'),
        ];
    }
}
