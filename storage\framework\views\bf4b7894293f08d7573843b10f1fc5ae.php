<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
            <div class="flex flex-wrap gap-4 items-center">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Filter Entitas</label>
                    <select id="entitas-filter" class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                        <option value="">Semua Entitas</option>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = \App\Models\Entitas::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entitas): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($entitas->id); ?>"><?php echo e($entitas->nama); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Filter Gudang</label>
                    <select id="warehouse-filter" class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                        <option value="">Semua Gudang</option>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = \App\Models\Warehouse::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warehouse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($warehouse->id); ?>"><?php echo e($warehouse->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </select>
                </div>
                <div class="flex items-end">
                    <button onclick="updateDashboard()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Update Dashboard
                    </button>
                </div>
            </div>
        </div>

        
        <?php
            $selectedEntitas = request('entitas_id');
            $selectedWarehouse = request('warehouse_id');

            // Base queries with filters
            $stockQuery = \App\Models\InventoryStock::query();
            $movementQuery = \App\Models\StockMovement::query();
            $adjustmentQuery = \App\Models\StockAdjustment::query();
            $transferQuery = \App\Models\StockTransfer::query();

            if ($selectedEntitas) {
                $stockQuery->where('entitas_id', $selectedEntitas);
                $movementQuery->where('entitas_id', $selectedEntitas);
                $adjustmentQuery->where('entitas_id', $selectedEntitas);
                $transferQuery->where('from_entitas_id', $selectedEntitas)->orWhere('to_entitas_id', $selectedEntitas);
            }

            if ($selectedWarehouse) {
                $stockQuery->where('warehouse_id', $selectedWarehouse);
                $movementQuery->where('warehouse_id', $selectedWarehouse);
                $adjustmentQuery->where('warehouse_id', $selectedWarehouse);
                $transferQuery->where('from_warehouse_id', $selectedWarehouse)->orWhere('to_warehouse_id', $selectedWarehouse);
            }

            // Calculate stats
            $totalProducts = \App\Models\Produk::count();
            $totalStockValue = $stockQuery->sum('total_value');
            $lowStockCount = $stockQuery->lowStock()->count();
            $todayMovements = $movementQuery->whereDate('movement_date', \Carbon\Carbon::today())->count();
            $pendingAdjustments = $adjustmentQuery->where('status', 'Draft')->count();
            $pendingTransfers = $transferQuery->whereIn('status', ['Pending', 'Approved', 'Shipped'])->count();
            $totalStockItems = $stockQuery->count();
            $outOfStockCount = $stockQuery->where('quantity', 0)->count();
        ?>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Produk</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($totalProducts)); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Nilai Total Stok</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white">Rp <?php echo e(number_format($totalStockValue, 0, ',', '.')); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-<?php echo e($lowStockCount > 0 ? 'red' : 'green'); ?>-600 dark:text-<?php echo e($lowStockCount > 0 ? 'red' : 'green'); ?>-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Stok Rendah</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($lowStockCount)); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pergerakan Hari Ini</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($todayMovements)); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-<?php echo e($pendingAdjustments > 0 ? 'orange' : 'green'); ?>-600 dark:text-<?php echo e($pendingAdjustments > 0 ? 'orange' : 'green'); ?>-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Penyesuaian Pending</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($pendingAdjustments)); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-<?php echo e($pendingTransfers > 0 ? 'blue' : 'green'); ?>-600 dark:text-<?php echo e($pendingTransfers > 0 ? 'blue' : 'green'); ?>-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Transfer Pending</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($pendingTransfers)); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Item Stok</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($totalStockItems)); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-<?php echo e($outOfStockCount > 0 ? 'red' : 'green'); ?>-600 dark:text-<?php echo e($outOfStockCount > 0 ? 'red' : 'green'); ?>-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Stok Habis</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e(number_format($outOfStockCount)); ?></p>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Aksi Cepat
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="<?php echo e(route('filament.admin.resources.stock-movements.index')); ?>"
                   class="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Pergerakan Stok</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Lihat riwayat pergerakan</p>
                    </div>
                </a>

                <a href="<?php echo e(route('filament.admin.resources.stock-adjustments.index')); ?>"
                   class="flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Penyesuaian Stok</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Kelola penyesuaian</p>
                    </div>
                </a>

                <a href="<?php echo e(route('filament.admin.resources.stock-transfers.index')); ?>"
                   class="flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Transfer Stok</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Transfer antar lokasi</p>
                    </div>
                </a>

                <a href="<?php echo e(route('filament.admin.resources.inventory-stocks.index')); ?>"
                   class="flex items-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Stok Saat Ini</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Lihat stok tersedia</p>
                    </div>
                </a>

                <a href="<?php echo e(route('filament.admin.resources.stock-opnames.index')); ?>"
                   class="flex items-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg hover:bg-indigo-100 dark:hover:bg-indigo-900/30 transition-colors">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Stock Opname</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Kelola stock opname</p>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <script>
        function updateDashboard() {
            const entitasId = document.getElementById('entitas-filter').value;
            const warehouseId = document.getElementById('warehouse-filter').value;

            let url = new URL(window.location);

            if (entitasId) {
                url.searchParams.set('entitas_id', entitasId);
            } else {
                url.searchParams.delete('entitas_id');
            }

            if (warehouseId) {
                url.searchParams.set('warehouse_id', warehouseId);
            } else {
                url.searchParams.delete('warehouse_id');
            }

            window.location.href = url.toString();
        }

        // Set selected values from URL parameters
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const entitasId = urlParams.get('entitas_id');
            const warehouseId = urlParams.get('warehouse_id');

            if (entitasId) {
                document.getElementById('entitas-filter').value = entitasId;
            }

            if (warehouseId) {
                document.getElementById('warehouse-filter').value = warehouseId;
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/pages/inventory-dashboard.blade.php ENDPATH**/ ?>