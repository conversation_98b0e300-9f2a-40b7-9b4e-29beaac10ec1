<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CutiIzin extends Model
{
    use HasFactory;

    protected $table = 'cuti_izin';

    protected $fillable = [
        'karyawan_id',
        'jenis_permohonan',
        'tanggal_mulai',
        'tanggal_selesai',
        'jumlah_hari',
        'alasan',
        'keterangan_tambahan',
        'dokumen_pendukung',
        'status',
        'approved_by',
        'approved_at',
        'rejection_reason',
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'approved_at' => 'datetime',
        'jumlah_hari' => 'integer',
    ];

    /**
     * Get the employee associated with this leave request
     */
    public function karyawan()
    {
        return $this->belongsTo(Karyawan::class);
    }

    /**
     * Get the supervisor who approved/rejected this request
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Check if the request is approved
     */
    public function getIsApprovedAttribute()
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the request is pending
     */
    public function getIsPendingAttribute()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the request is rejected
     */
    public function getIsRejectedAttribute()
    {
        return $this->status === 'rejected';
    }

    /**
     * Get the status label
     */
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'pending' => 'Menunggu Persetujuan',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            default => 'Tidak Diketahui'
        };
    }

    /**
     * Get the request type label
     */
    public function getJenisPermohonanLabelAttribute()
    {
        return match($this->jenis_permohonan) {
            'cuti' => 'Cuti',
            'izin' => 'Izin',
            'sakit' => 'Sakit',
            default => 'Tidak Diketahui'
        };
    }

    /**
     * Get the status color for UI
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            default => 'gray'
        };
    }

    /**
     * Calculate the number of days automatically
     */
    public function calculateJumlahHari()
    {
        if (!$this->tanggal_mulai || !$this->tanggal_selesai) {
            return 0;
        }

        $startDate = Carbon::parse($this->tanggal_mulai);
        $endDate = Carbon::parse($this->tanggal_selesai);

        // Calculate working days (excluding weekends)
        $days = 0;
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            // Only count weekdays (Monday to Friday)
            if ($currentDate->isWeekday()) {
                $days++;
            }
            $currentDate->addDay();
        }

        return $days;
    }

    /**
     * Boot method to automatically calculate days
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            // Automatically calculate jumlah_hari when saving
            $model->jumlah_hari = $model->calculateJumlahHari();
        });
    }

    /**
     * Scope for filtering by employee
     */
    public function scopeForKaryawan($query, $karyawanId)
    {
        return $query->where('karyawan_id', $karyawanId);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for filtering by request type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('jenis_permohonan', $type);
    }

    /**
     * Scope for filtering by date range
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('tanggal_mulai', [$startDate, $endDate])
                    ->orWhereBetween('tanggal_selesai', [$startDate, $endDate]);
    }

    /**
     * Check if dates overlap with existing approved requests
     */
    public function hasOverlapWithApprovedRequests()
    {
        return static::where('karyawan_id', $this->karyawan_id)
            ->where('status', 'approved')
            ->where('id', '!=', $this->id ?? 0)
            ->where(function ($query) {
                $query->whereBetween('tanggal_mulai', [$this->tanggal_mulai, $this->tanggal_selesai])
                      ->orWhereBetween('tanggal_selesai', [$this->tanggal_mulai, $this->tanggal_selesai])
                      ->orWhere(function ($subQuery) {
                          $subQuery->where('tanggal_mulai', '<=', $this->tanggal_mulai)
                                   ->where('tanggal_selesai', '>=', $this->tanggal_selesai);
                      });
            })
            ->exists();
    }

    /**
     * Validate if the request dates are valid
     */
    public function validateDates()
    {
        $errors = [];

        // Check if start date is not in the past (except for today)
        if ($this->tanggal_mulai && Carbon::parse($this->tanggal_mulai)->lt(Carbon::today())) {
            $errors[] = 'Tanggal mulai tidak boleh di masa lalu.';
        }

        // Check if end date is not before start date
        if ($this->tanggal_mulai && $this->tanggal_selesai &&
            Carbon::parse($this->tanggal_selesai)->lt(Carbon::parse($this->tanggal_mulai))) {
            $errors[] = 'Tanggal selesai tidak boleh sebelum tanggal mulai.';
        }

        // Check for overlapping approved requests
        if ($this->hasOverlapWithApprovedRequests()) {
            $errors[] = 'Tanggal yang dipilih bertabrakan dengan permohonan yang sudah disetujui.';
        }

        return $errors;
    }

    /**
     * Get formatted date range
     */
    public function getFormattedDateRangeAttribute()
    {
        if (!$this->tanggal_mulai || !$this->tanggal_selesai) {
            return '-';
        }

        $start = Carbon::parse($this->tanggal_mulai)->format('d M Y');
        $end = Carbon::parse($this->tanggal_selesai)->format('d M Y');

        if ($start === $end) {
            return $start;
        }

        return "{$start} - {$end}";
    }
}
