<?php

namespace App\Filament\Karyawan\Resources;

use App\Filament\Karyawan\Resources\CutiIzinResource\Pages;
use App\Models\CutiIzin;
use App\Models\Karyawan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CutiIzinResource extends Resource
{
    protected static ?string $model = CutiIzin::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';
    protected static ?string $navigationLabel = 'Cuti, Izin & Sakit';
    protected static ?int $navigationSort = 3;
    protected static ?string $pluralModelLabel = 'Cuti, Izin & Sakit';
    protected static ?string $modelLabel = 'Cuti, Izin & Sakit';

    public static function canAccess(): bool
    {
        $user = Auth::user();
        return $user?->role === 'karyawan';
    }

    public static function canCreate(): bool
    {
        return Auth::user()->role === 'karyawan';
    }

    public static function form(Form $form): Form
    {
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        return $form
            ->schema([
                Forms\Components\Hidden::make('karyawan_id')
                    ->default($karyawan?->id),

                Forms\Components\Section::make('Informasi Permohonan')
                    ->description('Silahkan isi form permohonan cuti atau izin dengan lengkap')
                    ->schema([
                        Forms\Components\Select::make('jenis_permohonan')
                            ->label('Jenis Permohonan')
                            ->options([
                                'cuti' => 'Cuti',
                                'izin' => 'Izin',
                                'sakit' => 'Sakit',
                            ])
                            ->required()
                            ->placeholder('Pilih jenis permohonan')
                            ->helperText('Pilih "Cuti" untuk cuti tahunan/khusus, "Izin" untuk izin sementara, atau "Sakit" untuk cuti sakit'),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('tanggal_mulai')
                                    ->label('Tanggal Mulai')
                                    ->required()
                                    ->minDate(Carbon::today())
                                    ->placeholder('Pilih tanggal mulai')
                                    ->helperText('Tanggal mulai cuti/izin')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $tanggalSelesai = $get('tanggal_selesai');
                                        if ($state && $tanggalSelesai) {
                                            $model = new CutiIzin([
                                                'tanggal_mulai' => $state,
                                                'tanggal_selesai' => $tanggalSelesai
                                            ]);
                                            $set('jumlah_hari', $model->calculateJumlahHari());
                                        }
                                    }),

                                Forms\Components\DatePicker::make('tanggal_selesai')
                                    ->label('Tanggal Selesai')
                                    ->required()
                                    ->minDate(fn(callable $get) => $get('tanggal_mulai') ?: Carbon::today())
                                    ->placeholder('Pilih tanggal selesai')
                                    ->helperText('Tanggal selesai cuti/izin')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $tanggalMulai = $get('tanggal_mulai');
                                        if ($state && $tanggalMulai) {
                                            $model = new CutiIzin([
                                                'tanggal_mulai' => $tanggalMulai,
                                                'tanggal_selesai' => $state
                                            ]);
                                            $set('jumlah_hari', $model->calculateJumlahHari());
                                        }
                                    }),
                            ]),

                        Forms\Components\TextInput::make('jumlah_hari')
                            ->label('Jumlah Hari Kerja')
                            ->numeric()
                            ->disabled()
                            ->helperText('Jumlah hari kerja akan dihitung otomatis (tidak termasuk weekend)')
                            ->suffix('hari'),

                        Forms\Components\Textarea::make('alasan')
                            ->label('Alasan')
                            ->required()
                            ->maxLength(1000)
                            ->placeholder('Jelaskan alasan permohonan cuti/izin Anda')
                            ->helperText('Jelaskan dengan jelas alasan permohonan cuti atau izin Anda')
                            ->rows(3),

                        Forms\Components\Textarea::make('keterangan_tambahan')
                            ->label('Keterangan Tambahan')
                            ->maxLength(1000)
                            ->placeholder('Informasi tambahan jika diperlukan')
                            ->helperText('Informasi tambahan yang mendukung permohonan (opsional)')
                            ->rows(2),

                        Forms\Components\FileUpload::make('dokumen_pendukung')
                            ->label('Dokumen Pendukung')
                            ->directory('cuti-izin/dokumen')
                            ->disk('public')
                            ->maxSize(5120)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf'])
                            ->helperText('Upload dokumen pendukung jika diperlukan (maksimal 5MB)')
                            ->placeholder('Pilih file dokumen pendukung'),
                    ])
                    ->collapsible()
                    ->persistCollapsed(false),

                Forms\Components\Section::make('Informasi Status')
                    ->description('Status permohonan akan diperbarui setelah disetujui supervisor')
                    ->schema([
                        Forms\Components\Placeholder::make('status_info')
                            ->label('Status Permohonan')
                            ->content(function ($record) {
                                if (!$record) {
                                    return 'Permohonan baru akan berstatus "Menunggu Persetujuan"';
                                }

                                $statusText = $record->status_label;

                                if ($record->approved_by && $record->approved_at) {
                                    $approver = $record->approvedBy->name ?? 'Unknown';
                                    $approvedDate = $record->approved_at->format('d M Y H:i');
                                    $statusText .= " oleh {$approver} pada {$approvedDate}";
                                }

                                if ($record->status === 'rejected' && $record->rejection_reason) {
                                    $statusText .= "\nAlasan penolakan: {$record->rejection_reason}";
                                }

                                return $statusText;
                            }),
                    ])
                    ->visible(fn($record) => $record !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('jenis_permohonan')
                    ->label('Jenis')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match($state) {
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match($state) {
                        'cuti' => 'primary',
                        'izin' => 'info',
                        'sakit' => 'danger',
                        default => 'gray'
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('formatted_date_range')
                    ->label('Periode')
                    ->sortable(['tanggal_mulai']),

                Tables\Columns\TextColumn::make('jumlah_hari')
                    ->label('Jumlah Hari')
                    ->suffix(' hari')
                    ->alignCenter()
                    ->sortable(),

                Tables\Columns\TextColumn::make('alasan')
                    ->label('Alasan')
                    ->limit(50)
                    ->tooltip(function ($record) {
                        return $record->alasan;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match($state) {
                        'pending' => 'Menunggu',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                        default => ucfirst($state)
                    })
                    ->color(fn(string $state): string => match($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray'
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tanggal Pengajuan')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('jenis_permohonan')
                    ->label('Jenis Permohonan')
                    ->options([
                        'cuti' => 'Cuti',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Menunggu Persetujuan',
                        'approved' => 'Disetujui',
                        'rejected' => 'Ditolak',
                    ]),

                Tables\Filters\Filter::make('tanggal_mulai')
                    ->form([
                        Forms\Components\DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_mulai', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_selesai', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->status === 'pending'),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn($record) => $record->status === 'pending'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn($records) => $records && $records->every(fn($record) => $record->status === 'pending')),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCutiIzins::route('/'),
            'create' => Pages\CreateCutiIzin::route('/create'),
            'view' => Pages\ViewCutiIzin::route('/{record}'),
            'edit' => Pages\EditCutiIzin::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $user = Auth::user();
        $karyawan = Karyawan::where('id_user', $user->id)->first();

        return parent::getEloquentQuery()
            ->with([
                'karyawan:id,nama_lengkap,nip',
                'approvedBy:id,name'
            ])
            ->where('karyawan_id', $karyawan?->id);
    }
}
