<div class="space-y-6">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Denda Bulan Ini -->
        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-red-400 dark:text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-red-500 dark:text-red-400 truncate">Total Denda Bulan Ini</dt>
                        <dd class="text-lg font-bold text-red-900 dark:text-red-200">
                            Rp <?php echo e(number_format($dendaBulanIni, 0, ',', '.')); ?>

                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Denda 3 Bulan Terakhir -->
        <div class="bg-orange-50 dark:bg-orange-900/20 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-orange-400 dark:text-orange-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-orange-500 dark:text-orange-400 truncate">Total Denda 3 Bulan Terakhir</dt>
                        <dd class="text-lg font-bold text-orange-900 dark:text-orange-200">
                            Rp <?php echo e(number_format($denda3Bulan, 0, ',', '.')); ?>

                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Breakdown Per Jenis Pelanggaran -->
    <!--[if BLOCK]><![endif]--><?php if($pelanggaranPerJenis->count() > 0): ?>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Breakdown Per Jenis Pelanggaran</h4>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Jenis Pelanggaran
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Jumlah Kasus
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Total Denda
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $pelanggaranPerJenis; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jenis): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                                <?php echo e($jenis['nama']); ?>

                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            <?php echo e($jenis['jumlah']); ?> kasus
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-red-600 dark:text-red-400">
                            Rp <?php echo e(number_format($jenis['total_denda'], 0, ',', '.')); ?>

                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </tbody>
                <tfoot class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-white">
                            Total
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-white">
                            <?php echo e($pelanggaranPerJenis->sum('jumlah')); ?> kasus
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-red-600 dark:text-red-400">
                            Rp <?php echo e(number_format($pelanggaranPerJenis->sum('total_denda'), 0, ',', '.')); ?>

                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <?php else: ?>
    <div class="bg-green-50 dark:bg-green-900/20 rounded-lg shadow p-6 text-center">
        <svg class="mx-auto h-12 w-12 text-green-400 dark:text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-green-900 dark:text-green-200">Tidak Ada Pelanggaran</h3>
        <p class="mt-1 text-sm text-green-500 dark:text-green-400">Anda tidak memiliki catatan pelanggaran.</p>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Informasi Tambahan -->
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg shadow p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400 dark:text-blue-300" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Informasi Penting</h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Denda pelanggaran akan dipotong dari gaji pada periode payroll berikutnya</li>
                        <li>Untuk menghindari pelanggaran, pastikan mematuhi semua peraturan perusahaan</li>
                        <li>Jika ada keberatan terhadap pelanggaran, segera hubungi HRD</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/karyawan/pelanggaran-summary.blade.php ENDPATH**/ ?>