<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureKaryawanRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Allow access to login page even if not authenticated or not a karyawan
        if ($request->routeIs('filament.karyawan.auth.login')) {
            return $next($request);
        }

        // If not logged in, redirect to login
        if (! Auth::check()) {
            return redirect()->route('filament.karyawan.auth.login');
        }

        // If logged in but not a karyawan (simplified - no Shield dependency)
        $user = Auth::user();
        $hasAccess = $user->role === 'karyawan';

        if (!$hasAccess) {
            // If user is admin or supervisor, redirect to admin panel
            if (in_array($user->role, ['admin', 'supervisor'])) {
                return redirect()->route('filament.admin.pages.dashboard');
            }

            // For other roles or no role, logout and redirect to login
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('filament.karyawan.auth.login')
                ->with('error', 'Anda tidak memiliki akses ke halaman karyawan.');
        }

        return $next($request);
    }
}
