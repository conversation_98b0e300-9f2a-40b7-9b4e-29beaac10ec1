<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Header with Project Selection and Filters -->
    <div class="space-y-6">
        <!-- Project Selection -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <div class="min-w-0 flex-1">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Pilih Kegiatan
                        </label>
                        <select wire:model.live="selectedProjectId"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">Pilih Kegiatan...</option>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($project->id); ?>"><?php echo e($project->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </div>
                </div>

                <!--[if BLOCK]><![endif]--><?php if($selectedProject): ?>
                    <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('heroicon-m-briefcase'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(BladeUI\Icons\Components\Svg::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                        <span><?php echo e($selectedProject->name); ?></span>
                        <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded text-xs">
                            <?php echo e($selectedProject->tasks->count()); ?> tugas
                        </span>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>

        <!-- Advanced Filters -->
        <!--[if BLOCK]><![endif]--><?php if($selectedProject): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div class="flex flex-col lg:flex-row lg:items-center gap-4">
                    <!-- Search -->
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cari Tugas</label>
                        <input type="text"
                               wire:model.live.debounce.300ms="searchTerm"
                               placeholder="Cari berdasarkan nama atau deskripsi..."
                               class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    </div>

                    <!-- Assignee Filter -->
                    <div class="min-w-0 lg:w-48">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Assignee</label>
                        <select wire:model.live="filterAssignee"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">Semua assignee</option>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $teamMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($member->id); ?>"><?php echo e($member->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </div>

                    <!-- Due Date Filter -->
                    <div class="min-w-0 lg:w-48">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Batas Waktu</label>
                        <select wire:model.live="filterDueDate"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">Semua tanggal</option>
                            <option value="overdue">Terlambat</option>
                            <option value="today">Hari ini</option>
                            <option value="this_week">Minggu ini</option>
                            <option value="no_due_date">Tidak ada batas waktu</option>
                        </select>
                    </div>

                    <!-- Swimlane Toggle -->
                    <div class="min-w-0 lg:w-48">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Group By</label>
                        <select wire:model.live="swimlaneBy"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="none">Tidak ada pengelompokan</option>
                            <option value="assignee">Berdasarkan assignee</option>
                            <option value="priority">Berdasarkan prioritas</option>
                        </select>
                    </div>

                    <!-- Clear Filters -->
                    <div class="flex items-end">
                        <button wire:click="clearFilters"
                                class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            Bersihkan
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Kanban Board -->
        <!--[if BLOCK]><![endif]--><?php if($selectedProject): ?>
            <!--[if BLOCK]><![endif]--><?php if($swimlaneBy === 'none'): ?>
                <!-- Standard Kanban View -->
                <div x-data="enhancedDragDropHandler()"
                     x-init="init()"
                     @task-moved.window="init()"
                     @task-updated.window="init()"
                     @refresh-board.window="init()"
                     class="relative overflow-x-auto pb-6">

                    <div class="inline-flex gap-6 pb-2 min-w-full">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $taskStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="kanban-column bg-gray-50 dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-700 flex flex-col"
                                 style="width: calc(85vw - 2rem); min-width: 320px; max-width: 380px; @media (min-width: 640px) { width: calc((100vw - 6rem) / 2); } @media (min-width: 1024px) { width: calc((100vw - 8rem) / 3); } @media (min-width: 1280px) { width: calc((100vw - 10rem) / 4); }"
                                 data-status-id="<?php echo e($status->id); ?>">

                                <!-- Column Header -->
                                <div class="px-4 py-3 rounded-t-xl border-b border-gray-200 dark:border-gray-700"
                                     style="background: linear-gradient(135deg, <?php echo e($status->color); ?>15, <?php echo e($status->color); ?>25);">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-3 h-3 rounded-full" style="background-color: <?php echo e($status->color); ?>;"></div>
                                            <h3 class="font-semibold text-gray-900 dark:text-white"><?php echo e($status->name); ?></h3>
                                            <span class="px-2 py-1 text-xs font-medium bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full">
                                                <?php echo e($status->current_count); ?>

                                            </span>
                                        </div>

                                        <!--[if BLOCK]><![endif]--><?php if($showWipLimits && $status->wip_limit < 999): ?>
                                            <div class="flex items-center space-x-1">
                                                <span class="text-xs text-gray-500 dark:text-gray-400">WIP:</span>
                                                <span class="text-xs font-medium <?php echo e($status->wip_status === 'exceeded' ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'); ?>">
                                                    <?php echo e($status->current_count); ?>/<?php echo e($status->wip_limit); ?>

                                                </span>
                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>

                                    <!--[if BLOCK]><![endif]--><?php if($showWipLimits && $status->wip_limit < 999): ?>
                                        <div class="mt-2">
                                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                                                <div class="h-1 rounded-full transition-all duration-300 <?php echo e($status->wip_status === 'exceeded' ? 'bg-red-500' : 'bg-blue-500'); ?>"
                                                     style="width: <?php echo e(min(($status->current_count / $status->wip_limit) * 100, 100)); ?>%"></div>
                                            </div>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>

                                <!-- Tasks Container -->
                                <div class="p-3 flex flex-col gap-3 h-[calc(100vh-24rem)] overflow-y-auto">
                                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $status->tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <?php echo $__env->make('filament.components.enhanced-task-card', ['task' => $task], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <div class="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
                                            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('heroicon-o-plus'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(BladeUI\Icons\Components\Svg::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-8 h-8 mb-2']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                            <p class="text-sm">Drop tugas di sini</p>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php else: ?>
                <!-- Swimlane View -->
                <div class="space-y-6">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getFilteredTasksByAssignee(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $swimlane): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                            <!-- Swimlane Header -->
                            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 rounded-t-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                        <?php echo e(substr($swimlane->user->name, 0, 2)); ?>

                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900 dark:text-white"><?php echo e($swimlane->user->name); ?></h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($swimlane->user->email); ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Swimlane Columns -->
                            <div class="p-4">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $swimlane->statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
                                            <div class="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                                                <div class="flex items-center justify-between">
                                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($status->name); ?></h4>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($status->current_count); ?></span>
                                                </div>
                                            </div>
                                            <div class="p-2 space-y-2 max-h-64 overflow-y-auto">
                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $status->tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php echo $__env->make('filament.components.enhanced-task-card', ['task' => $task, 'compact' => true], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php else: ?>
            <!-- No Project Selected -->
            <div class="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400 gap-4">
                <div class="flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 p-6">
                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('heroicon-o-squares-2x2'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(BladeUI\Icons\Components\Svg::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-16 h-16 text-gray-400 dark:text-gray-500']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                </div>
                <h2 class="text-xl font-medium text-gray-600 dark:text-gray-300">Pilih Kegiatan</h2>
                <p class="text-sm text-gray-500 dark:text-gray-400 text-center">
                    Pilih kegiatan dari dropdown di atas untuk melihat papan kanban yang ditingkatkan
                </p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Enhanced Drag and Drop Script -->
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('enhancedDragDropHandler', () => ({
                draggingTask: null,
                dragOverColumn: null,

                init() {
                    this.$nextTick(() => {
                        this.removeAllEventListeners();
                        this.attachAllEventListeners();
                    });
                },

                removeAllEventListeners() {
                    const tasks = document.querySelectorAll('.enhanced-task-card');
                    tasks.forEach(task => {
                        task.removeAttribute('draggable');
                        const newTask = task.cloneNode(true);
                        task.parentNode.replaceChild(newTask, task);
                    });
                },

                attachAllEventListeners() {
                    const tasks = document.querySelectorAll('.enhanced-task-card');
                    const columns = document.querySelectorAll('.kanban-column');

                    // Task drag events
                    tasks.forEach(task => {
                        task.setAttribute('draggable', 'true');

                        task.addEventListener('dragstart', (e) => {
                            this.draggingTask = task.getAttribute('data-task-id');
                            task.classList.add('opacity-50', 'scale-95');
                            e.dataTransfer.effectAllowed = 'move';
                        });

                        task.addEventListener('dragend', (e) => {
                            task.classList.remove('opacity-50', 'scale-95');
                            this.draggingTask = null;
                            this.dragOverColumn = null;
                        });
                    });

                    // Column drop events
                    columns.forEach(column => {
                        column.addEventListener('dragover', (e) => {
                            e.preventDefault();
                            e.dataTransfer.dropEffect = 'move';

                            if (this.dragOverColumn !== column) {
                                // Remove highlight from previous column
                                if (this.dragOverColumn) {
                                    this.dragOverColumn.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                                }

                                // Highlight current column
                                column.classList.add('ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                                this.dragOverColumn = column;
                            }
                        });

                        column.addEventListener('dragleave', (e) => {
                            // Only remove highlight if leaving the column entirely
                            if (!column.contains(e.relatedTarget)) {
                                column.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
                                this.dragOverColumn = null;
                            }
                        });

                        column.addEventListener('drop', (e) => {
                            e.preventDefault();
                            column.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');

                            if (this.draggingTask) {
                                const statusId = column.getAttribute('data-status-id');
                                const taskId = this.draggingTask;
                                this.draggingTask = null;
                                this.dragOverColumn = null;

                                // Use Alpine.js $wire magic method
                                this.$wire.call('moveTask', parseInt(taskId), statusId);
                            }
                        });
                    });
                }
            }));
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/pages/modern-kanban-board.blade.php ENDPATH**/ ?>