<?php

namespace App\Filament\Karyawan\Resources\CutiIzinResource\Pages;

use App\Filament\Karyawan\Resources\CutiIzinResource;
use App\Models\CutiIzin;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class CreateCutiIzin extends CreateRecord
{
    protected static string $resource = CutiIzinResource::class;

    public function getTitle(): string
    {
        return 'Ajukan Cuti/Izin/Sakit';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function handleRecordCreation(array $data): Model
    {
        // Validate dates before creating
        $model = new CutiIzin($data);
        $errors = $model->validateDates();

        if (!empty($errors)) {
            foreach ($errors as $error) {
                Notification::make()
                    ->title('Validasi Gagal')
                    ->body($error)
                    ->danger()
                    ->send();
            }

            $this->halt();
        }

        // Create the record
        $record = static::getModel()::create($data);

        // Send success notification
        Notification::make()
            ->title('Permohonan Berhasil Diajukan')
            ->body('Permohonan cuti/izin Anda telah berhasil diajukan dan menunggu persetujuan supervisor.')
            ->success()
            ->send();

        return $record;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure status is set to pending
        $data['status'] = 'pending';

        return $data;
    }
}
