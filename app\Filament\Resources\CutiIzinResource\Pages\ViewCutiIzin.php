<?php

namespace App\Filament\Resources\CutiIzinResource\Pages;

use App\Filament\Resources\CutiIzinResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCutiIzin extends ViewRecord
{
    protected static string $resource = CutiIzinResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return 'Detail Permohonan Cuti/Izin';
    }
}
