<?php

namespace App\Filament\Resources\KaryawanResource\Pages;

use Filament\Resources\Pages\ViewRecord;
use App\Filament\Resources\KaryawanResource;
use App\Filament\Resources\KaryawanResource\Widgets\KaryawanDetailSummary;
use App\Filament\Resources\KaryawanResource\RelationManagers\RiwayatKontrakRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\PenggajianRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\PendidikanRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\KerabatRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\BpjsRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\ResignRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\KpiPenilaianRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\PelanggaranRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\SchedulesRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\DokumenRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\CutiIzinRelationManager;
use App\Filament\Resources\KaryawanResource\RelationManagers\MutasiPromosiDemosiRelationManager;

use App\Filament\Resources\KaryawanResource\Widgets\KontrakAktifSummary;





class ViewKaryawan extends ViewRecord
{
    protected static string $resource = KaryawanResource::class;

    /**
     * Menampilkan widget di bagian atas halaman detail karyawan
     */
    protected function getHeaderWidgets(): array
    {
        return [
            KaryawanDetailSummary::class,

        ];
    }

    public function getRelationManagers(): array
    {
        return [
            RiwayatKontrakRelationManager::class,
            PenggajianRelationManager::class,
            PendidikanRelationManager::class,
            KerabatRelationManager::class,
            BpjsRelationManager::class,
            ResignRelationManager::class,
            KpiPenilaianRelationManager::class,
            PelanggaranRelationManager::class,
            DokumenRelationManager::class,
            SchedulesRelationManager::class,
            CutiIzinRelationManager::class,
            MutasiPromosiDemosiRelationManager::class,
        ];
    }

    public function hasCombinedRelationManagerTabsWithForm(): bool
    {
        return true;
    }



    /**
     * Menampilkan semua relasi yang berkaitan dengan karyawan di halaman detail
     */
}
