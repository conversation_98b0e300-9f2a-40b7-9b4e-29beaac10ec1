<?php

namespace App\Filament\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Traits\HasAdvancedDashboardFilters;
use Illuminate\Support\Facades\Auth;

class Dashboard extends BaseDashboard implements HasForms
{
    use InteractsWithForms, HasAdvancedDashboardFilters;

    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static ?string $title = 'Dashboard Admin';
    protected static ?string $navigationLabel = 'Dashboard Admin';
    protected static string $view = 'filament.pages.advanced-dashboard-with-filters';
    protected static string $routePath = '/dashboard';

    /**
     * Check if user can access this dashboard
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user->hasAnyRole(['super_admin', 'direktur', 'manager_hrd', 'manager_accounting']);
    }

    public function getWidgets(): array
    {
        return [
            \Filament\Widgets\AccountWidget::class,
            \App\Filament\Widgets\SystemOverviewWidget::class,
            \App\Filament\Widgets\QuickStatsWidget::class,
            \App\Filament\Widgets\RecentActivitiesWidget::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'sm' => 1,
            'md' => 2,
            'lg' => 3,
            'xl' => 4,
        ];
    }
}
