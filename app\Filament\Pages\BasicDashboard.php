<?php

namespace App\Filament\Pages;

use Filament\Pages\Dashboard as BaseDashboard;
use Illuminate\Support\Facades\Auth;

class BasicDashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static ?string $title = 'Dashboard';
    protected static ?string $navigationLabel = 'Dashboard';
    protected static string $routePath = '/dashboard';
    protected static ?int $navigationSort = -2;

    /**
     * Check if user can access this basic dashboard
     * Only manager_hrd and manager_accounting can access this dashboard
     */
    public static function canAccess(): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        return $user->hasAnyRole(['manager_hrd', 'manager_accounting']);
    }

    /**
     * Control navigation visibility based on permissions
     */
    public static function shouldRegisterNavigation(): bool
    {
        return static::canAccess();
    }

    public function getWidgets(): array
    {
        return [
            \Filament\Widgets\AccountWidget::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'sm' => 1,
            // 'md' => 2,
            // 'lg' => 2,
            // 'xl' => 3,
        ];
    }
}
